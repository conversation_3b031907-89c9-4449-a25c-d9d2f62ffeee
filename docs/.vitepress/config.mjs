import { defineConfig } from "vitepress";

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: "小明同学的宝藏",
  description:
    "精选实用网站工具、PC安卓游戏、软件资源、网盘分享。涵盖办公工具、学习网站、娱乐资源等，免费分享优质资源，提升工作学习效率。",
  lang: "zh-CN",

  cleanUrls: true, // 去掉 .html

  // 自动生成网站地图
  sitemap: {
    hostname: "https://xmtx.org",
  },

  // 禁用最后更新时间，避免批量提交造成的统一时间问题
  lastUpdated: false,

  // 动态生成页面 meta 标签
  transformHead: ({ pageData }) => {
    const head = [];

    // 动态设置页面标题和描述（仅保留基础SEO标签）
    // 注意：已移除 Open Graph 和 Twitter Card 标签，专注于搜索引擎优化

    // 统一 URL 格式 - 不使用尾部斜杠
    let relativePath = pageData.relativePath.replace(/\.md$/, "");

    // 处理 index 文件的特殊情况
    if (relativePath === "index" || relativePath === "") {
      relativePath = "";
    } else if (relativePath.endsWith("/index")) {
      // 移除路径末尾的 /index
      relativePath = relativePath.replace(/\/index$/, "");
    }

    let url = `https://xmtx.org/${relativePath}`;
    // 确保根路径以 / 结尾，其他路径不带 /
    if (relativePath === "") {
      url = "https://xmtx.org/";
    }
    head.push(["link", { rel: "canonical", href: url }]);

    return head;
  },
  head: [
    // 全局 SEO Meta 标签
    [
      "meta",
      {
        name: "keywords",
        content: "资源分享,网站收集,实用工具,网盘资源,游戏下载,软件分享",
      },
    ],
    ["meta", { name: "author", content: "小明同学" }],
    ["meta", { name: "robots", content: "index,follow" }],
    ["meta", { name: "googlebot", content: "index,follow" }],

    // 全局结构化数据 JSON-LD
    [
      "script",
      { type: "application/ld+json" },
      JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebSite",
        name: "小明同学的资源宝藏",
        description:
          "精选实用网站工具、PC安卓游戏、软件资源、网盘分享。涵盖办公工具、学习网站、娱乐资源等，免费分享优质资源，提升工作学习效率。",
        url: "https://xmtx.org/",
        author: {
          "@type": "Person",
          name: "小明同学",
        },
        potentialAction: {
          "@type": "SearchAction",
          target: "https://xmtx.org/search?q={search_term_string}",
          "query-input": "required name=search_term_string",
        },
      }),
    ],

    // 添加网站图标
    ["link", { rel: "icon", href: "/favicon.ico" }],
    [
      "link",
      {
        rel: "icon",
        type: "image/png",
        sizes: "16x16",
        href: "/favicon-16x16.png",
      },
    ],
    [
      "link",
      {
        rel: "icon",
        type: "image/png",
        sizes: "32x32",
        href: "/favicon-32x32.png",
      },
    ],
    [
      "link",
      {
        rel: "icon",
        type: "image/png",
        sizes: "192x192",
        href: "/favicon-192x192.png",
      },
    ],
    [
      "link",
      {
        rel: "icon",
        type: "image/png",
        sizes: "512x512",
        href: "/favicon-512x512.png",
      },
    ],
    ["link", { rel: "apple-touch-icon", href: "/apple-touch-icon.png" }],
    // Google Analytics
    [
      "script",
      {
        async: true,
        src: "https://www.googletagmanager.com/gtag/js?id=G-QP4DG5B7BR",
      },
    ],
    [
      "script",
      {},
      `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-QP4DG5B7BR');
      `,
    ],
    // Umami Analytics
    [
      "script",
      {
        defer: true,
        src: "https://umami-amber-gamma.vercel.app/script.js",
        "data-website-id": "a7e4455f-72ae-477b-8e2f-e03e7056e8e3",
      },
    ],
    // Cloudflare Web Analytics
    [
      "script",
      {
        defer: true,
        src: "https://static.cloudflareinsights.com/beacon.min.js",
        "data-cf-beacon": '{"token": "b685ed62462e4edab724374cfd6045aa"}',
      },
    ],
  ],
  themeConfig: {
    // https://vitepress.dev/reference/default-theme-config
    nav: [
      { text: "首页", link: "/" },
      { text: "网站资源", link: "/websites/" },
      { text: "网盘资源", link: "/cloud/" },
      { text: "游戏资源", link: "/games/" },
      { text: "视频资源", link: "/videos/" },
      { text: "关于", link: "/about" },
    ],

    sidebar: {
      // 游戏资源和网盘游戏共享侧边栏
      "/games/": [
        {
          text: "网盘资源",
          items: [
            { text: "首页", link: "/cloud/" },
            {
              text: "游戏",
              collapsed: false,
              items: [
                { text: "电脑游戏", link: "/games/pc/" },
                {
                  text: "安卓游戏",
                  link: "/games/android/",
                },
              ],
            },
            {
              text: "视频",
              collapsed: false,
              items: [
                {
                  text: "B站付费课程",
                  link: "/videos/bilibili/",
                },
                {
                  text: "纪录片合集",
                  link: "/videos/docu/",
                },
              ],
            },
          ],
        },
      ],
      // 视频资源统一侧边栏配置
      "/videos/": [
        {
          text: "网盘资源",
          items: [
            { text: "首页", link: "/cloud/" },
            {
              text: "游戏",
              collapsed: false,
              items: [
                { text: "电脑游戏", link: "/games/pc/" },
                {
                  text: "安卓游戏",
                  link: "/games/android/",
                },
              ],
            },
            {
              text: "视频",
              collapsed: false,
              items: [
                {
                  text: "B站付费课程",
                  link: "/videos/bilibili/",
                },
                {
                  text: "纪录片合集",
                  link: "/videos/docu/",
                },
              ],
            },
          ],
        },
      ],
      // 网站资源专属侧边栏
      "/websites/": [
        {
          text: "网站资源",
          items: [
            { text: "首页", link: "/websites/" },
            { text: "搜索引擎", link: "/websites/search-engines/" },
            { text: "实用工具", link: "/websites/practical-tools/" },
            { text: "办公必备", link: "/websites/office-essentials/" },
            {
              text: "学习提升",
              link: "/websites/learning-improvement/",
            },
            { text: "休闲娱乐", link: "/websites/entertainment/" },
            { text: "生活相关", link: "/websites/life-related/" },
            { text: "打印机专区", link: "/websites/printer-zone/" },
          ],
        },
      ],
      // 网盘资源专属侧边栏
      "/cloud/": [
        {
          text: "网盘资源",
          items: [
            { text: "首页", link: "/cloud/" },
            {
              text: "游戏",
              collapsed: false,
              items: [
                { text: "电脑游戏", link: "/games/pc/" },
                {
                  text: "安卓游戏",
                  link: "/games/android/",
                },
              ],
            },
            {
              text: "视频",
              collapsed: false,
              items: [
                {
                  text: "B站付费课程",
                  link: "/videos/bilibili/",
                },
                {
                  text: "纪录片合集",
                  link: "/videos/docu/",
                },
              ],
            },
          ],
        },
      ],
    },

    socialLinks: [
      { icon: "github", link: "https://github.com/your-username/resource-hub" },
    ],

    // 添加主题配置
    outlineTitle: "本页目录",
    returnToTopLabel: "返回顶部",
    darkModeSwitchLabel: "主题模式",
    sidebarMenuLabel: "菜单",
    lastUpdatedText: "最后更新",
    docFooter: {
      prev: "上一页",
      next: "下一页",
    },
    // 新增本地化文本
    langMenuLabel: "切换语言",
  },
});
